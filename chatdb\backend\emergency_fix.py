#!/usr/bin/env python3
"""
紧急修复：直接检查和修复字段映射问题
"""

import sqlite3
import sys
import os

def check_database_mappings():
    """检查数据库中的字段映射"""
    print("🔍 检查数据库中的字段映射")
    print("=" * 60)
    
    resource_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(resource_path)
    cursor = conn.cursor()
    
    try:
        # 1. 检查fin_data连接的字段
        cursor.execute('''
            SELECT sc.id, sc.column_name, st.table_name
            FROM schemacolumn sc
            JOIN schematable st ON sc.table_id = st.id
            JOIN dbconnection dc ON st.connection_id = dc.id
            WHERE dc.name = 'fin_data' AND st.table_name = 'financial_data'
            ORDER BY sc.column_name
        ''')
        
        columns = cursor.fetchall()
        print(f"📊 financial_data表的字段:")
        for col_id, col_name, table_name in columns:
            print(f"   ID:{col_id} - {col_name}")
        
        # 2. 检查关键字段的映射
        print(f"\n🔗 关键字段的映射情况:")
        
        key_fields = ['accounting_unit_name', 'year', 'month', 'debit_amount']
        
        for field in key_fields:
            cursor.execute('''
                SELECT vm.nl_term, vm.db_value, COUNT(*) as count
                FROM valuemapping vm
                JOIN schemacolumn sc ON vm.column_id = sc.id
                JOIN schematable st ON sc.table_id = st.id
                JOIN dbconnection dc ON st.connection_id = dc.id
                WHERE dc.name = 'fin_data' AND sc.column_name = ?
                GROUP BY vm.nl_term, vm.db_value
                ORDER BY vm.nl_term
            ''', (field,))
            
            mappings = cursor.fetchall()
            print(f"\n📋 {field} 的映射 ({len(mappings)} 个):")
            
            if mappings:
                for nl_term, db_value, count in mappings:
                    print(f"   '{nl_term}' → {db_value}")
            else:
                print(f"   ❌ 没有映射")
        
        # 3. 检查问题字段的映射
        print(f"\n🚨 检查问题字段的映射:")
        problem_terms = ['company_id', 'company_name', 'date', 'expense_date']
        
        for term in problem_terms:
            cursor.execute('''
                SELECT vm.nl_term, vm.db_value, sc.column_name
                FROM valuemapping vm
                JOIN schemacolumn sc ON vm.column_id = sc.id
                JOIN schematable st ON sc.table_id = st.id
                JOIN dbconnection dc ON st.connection_id = dc.id
                WHERE dc.name = 'fin_data' AND vm.nl_term = ?
            ''', (term,))
            
            mappings = cursor.fetchall()
            if mappings:
                for nl_term, db_value, column_name in mappings:
                    print(f"   ✅ '{nl_term}' → {db_value} (字段: {column_name})")
            else:
                print(f"   ❌ '{term}' 没有映射")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False
    finally:
        conn.close()

def add_critical_mappings():
    """添加关键映射"""
    print(f"\n🔧 添加关键映射")
    print("=" * 60)
    
    resource_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(resource_path)
    cursor = conn.cursor()
    
    try:
        # 获取字段ID
        cursor.execute('''
            SELECT sc.id, sc.column_name
            FROM schemacolumn sc
            JOIN schematable st ON sc.table_id = st.id
            JOIN dbconnection dc ON st.connection_id = dc.id
            WHERE dc.name = 'fin_data' AND st.table_name = 'financial_data'
        ''')
        
        field_ids = {row[1]: row[0] for row in cursor.fetchall()}
        
        # 定义关键映射
        critical_mappings = [
            # accounting_unit_name 字段
            ('accounting_unit_name', [
                'company_id', 'company_name', 'company', 'enterprise_id', 
                'enterprise_name', 'organization_id', 'organization_name',
                'unit_id', 'unit_name', '公司', '企业', '单位'
            ]),
            # year 字段
            ('year', [
                'date', 'expense_date', 'transaction_date', 'record_date',
                'fiscal_year', 'year_field', '年份', '日期'
            ]),
            # month 字段
            ('month', [
                'month_field', 'fiscal_month', 'period', '月份'
            ]),
            # debit_amount 字段
            ('debit_amount', [
                'expense_amount', 'cost_amount', 'debit_value', 
                'expense_value', 'cost_value', 'amount', '金额', '费用'
            ])
        ]
        
        added_count = 0
        
        for field_name, terms in critical_mappings:
            if field_name not in field_ids:
                print(f"⚠️ 字段 {field_name} 不存在，跳过")
                continue
                
            column_id = field_ids[field_name]
            print(f"\n📋 处理字段: {field_name} (ID: {column_id})")
            
            for term in terms:
                try:
                    # 检查是否已存在
                    cursor.execute('''
                        SELECT COUNT(*) FROM valuemapping 
                        WHERE column_id = ? AND nl_term = ?
                    ''', (column_id, term))
                    
                    if cursor.fetchone()[0] == 0:
                        # 添加映射
                        cursor.execute('''
                            INSERT INTO valuemapping (column_id, nl_term, db_value, created_at, updated_at)
                            VALUES (?, ?, ?, datetime('now'), datetime('now'))
                        ''', (column_id, term, field_name))
                        
                        added_count += 1
                        print(f"   ✅ 添加: '{term}' → {field_name}")
                    else:
                        print(f"   ⏭️ 跳过: '{term}' (已存在)")
                        
                except Exception as e:
                    print(f"   ❌ 添加失败: {term} - {e}")
        
        conn.commit()
        print(f"\n✅ 总共添加了 {added_count} 个映射")
        return True
        
    except Exception as e:
        print(f"❌ 添加映射失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def test_mapping_retrieval():
    """测试映射检索功能"""
    print(f"\n🧪 测试映射检索功能")
    print("=" * 60)
    
    try:
        # 添加当前目录到Python路径
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.services.text2sql_utils import get_value_mappings
        from app.db.session import SessionLocal
        
        db = SessionLocal()
        
        # 模拟schema_context
        schema_context = {
            'columns': [
                {'id': 4, 'name': 'accounting_unit_name', 'table_name': 'financial_data'},
                {'id': 1, 'name': 'year', 'table_name': 'financial_data'},
                {'id': 2, 'name': 'month', 'table_name': 'financial_data'},
                {'id': 27, 'name': 'debit_amount', 'table_name': 'financial_data'}
            ]
        }
        
        # 获取映射
        value_mappings = get_value_mappings(db, schema_context)
        
        print(f"📊 检索到的映射:")
        print(f"   映射字段数: {len(value_mappings)}")
        print(f"   总映射数: {sum(len(mappings) for mappings in value_mappings.values())}")
        
        # 检查关键映射
        key_terms = ['company_id', 'company_name', 'date', 'expense_date']
        
        for table_col, mappings in value_mappings.items():
            field_name = table_col.split('.')[-1]
            found_keys = [term for term in key_terms if term in mappings]
            
            if found_keys:
                print(f"   🔑 {field_name}: {found_keys}")
        
        # 生成映射字符串
        if value_mappings:
            mappings_str = "-- Value Mappings:\n"
            for column, mappings in value_mappings.items():
                mappings_str += f"-- For {column}:\n"
                for nl_term, db_value in list(mappings.items())[:3]:  # 只显示前3个
                    mappings_str += f"--   '{nl_term}' in natural language refers to '{db_value}' in the database\n"
            
            print(f"\n📝 映射字符串预览:")
            print(mappings_str[:500] + "..." if len(mappings_str) > 500 else mappings_str)
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def provide_emergency_solution():
    """提供紧急解决方案"""
    print(f"\n🚨 紧急解决方案")
    print("=" * 60)
    
    print("""
基于诊断结果，问题可能在于：

1️⃣ **映射数据不完整**
   - 数据库中缺少关键的字段映射
   - company_id → accounting_unit_name 映射缺失

2️⃣ **映射检索有问题**
   - get_value_mappings 函数没有正确工作
   - schema_context 传递有问题

3️⃣ **LLM提示构建有问题**
   - 映射信息没有被正确包含在提示中
   - 字段约束没有生效

---

🔧 **立即修复方案**:

1. 运行此脚本添加缺失的映射
2. 重启后端服务
3. 测试新的查询
4. 如果仍有问题，需要检查提示构建逻辑

---

📊 **预期效果**:

修复后，SQL应该生成：
```sql
SELECT 
    accounting_unit_name, 
    debit_amount
FROM 
    financial_data
WHERE 
    year = 2024 AND month = 4
    AND account_code LIKE '66%'
```

而不是错误的：
```sql
SELECT 
    company_id, 
    expense_amount
FROM 
    financial_data
WHERE 
    expense_date >= '2024-04-01'
```
""")

if __name__ == "__main__":
    print("🚨 紧急修复：字段映射问题")
    print("=" * 80)
    
    # 1. 检查当前映射状态
    mapping_ok = check_database_mappings()
    
    if mapping_ok:
        # 2. 添加关键映射
        add_ok = add_critical_mappings()
        
        if add_ok:
            # 3. 测试映射检索
            test_mapping_retrieval()
    
    # 4. 提供解决方案
    provide_emergency_solution()
    
    print(f"\n" + "=" * 80)
    print("🎯 请重启后端服务并重新测试")
    print("=" * 80)
