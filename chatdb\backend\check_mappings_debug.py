#!/usr/bin/env python3
"""
检查字段映射数据
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app import crud

def check_mappings():
    """检查数据库中的字段映射"""
    db = SessionLocal()
    try:
        print("=== 检查字段映射数据 ===")
        
        # 检查所有字段映射
        all_mappings = db.execute("SELECT COUNT(*) FROM valuemapping").fetchone()[0]
        print(f"总映射数量: {all_mappings}")
        
        if all_mappings > 0:
            mappings = db.execute("""
                SELECT vm.id, vm.column_id, vm.nl_term, vm.db_value, 
                       sc.table_name, sc.name as column_name
                FROM valuemapping vm
                JOIN schemacolumn sc ON vm.column_id = sc.id
                LIMIT 10
            """).fetchall()
            
            print("前10条映射:")
            for mapping in mappings:
                print(f"  {mapping[4]}.{mapping[5]}: '{mapping[2]}' → '{mapping[3]}'")
        
        # 检查financial_data表的字段
        print(f"\n=== 检查financial_data表字段 ===")
        columns = db.execute("""
            SELECT id, name FROM schemacolumn 
            WHERE table_name = 'financial_data'
        """).fetchall()
        
        print(f"financial_data表字段数量: {len(columns)}")
        for col in columns:
            print(f"  ID:{col[0]} - {col[1]}")
            
            # 检查每个字段的映射
            col_mappings = db.execute("""
                SELECT nl_term, db_value FROM valuemapping 
                WHERE column_id = ?
            """, (col[0],)).fetchall()
            
            if col_mappings:
                print(f"    映射数量: {len(col_mappings)}")
                for nl_term, db_value in col_mappings[:3]:
                    print(f"      '{nl_term}' → '{db_value}'")
            else:
                print(f"    ❌ 无映射")
                
    except Exception as e:
        print(f"错误: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_mappings()
